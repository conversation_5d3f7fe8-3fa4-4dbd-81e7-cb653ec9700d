@inherits LayoutComponentBase

<MudThemeProvider/>
<MudPopoverProvider/>
<MudDialogProvider/>
<MudSnackbarProvider/>

<MudLayout>
    <MudAppBar>
        <MudIconButton Icon="@Icons.Material.Filled.Menu" Color="Color.Inherit" Edge="Edge.Start" OnClick="@((e) => DrawerToggle())" />
        My Application
    </MudAppBar>
    <MudDrawer @bind-Open="@_drawerOpen">
        <MudNavMenu>
            <MudNavLink Href="/" Match="NavLinkMatch.All">Dashboard</MudNavLink>
            <MudNavLink Href="/servers" Match="NavLinkMatch.Prefix">Servers</MudNavLink>
            <MudNavGroup Title="Settings" Expanded="true">
                <MudNavLink Href="/users"  Match="NavLinkMatch.Prefix">Users</MudNavLink>
                <MudNavLink Href="/security"  Match="NavLinkMatch.Prefix">Security</MudNavLink>
            </MudNavGroup>
            <MudNavLink Href="/about"  Match="NavLinkMatch.Prefix">About</MudNavLink>
        </MudNavMenu>
    </MudDrawer>
    <MudMainContent>
        @Body
    </MudMainContent>
</MudLayout>

@code {
    bool _drawerOpen = true;

    void DrawerToggle()
    {
        _drawerOpen = !_drawerOpen;
    }
}